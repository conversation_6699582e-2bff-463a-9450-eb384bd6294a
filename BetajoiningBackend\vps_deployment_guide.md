# VPS Deployment Guide for LawVriksh Backend

## Prerequisites
- Ubuntu 20.04+ VPS
- Root or sudo access
- Domain name (optional but recommended)

## Step-by-Step Deployment

### 1. Initial Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3 python3-pip python3-venv mysql-server nginx git curl

# Install Docker (if using Docker deployment)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. Clone Repository
```bash
cd /opt
sudo git clone <your-repo-url> lawvriksh-backend
sudo chown -R $USER:$USER lawvriksh-backend
cd lawvriksh-backend/BetajoiningBackend
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit with your VPS values
nano .env
```

Required environment variables:
```env
# Database
DATABASE_URL=mysql+pymysql://lawvriksh_user:your_password@localhost/lawvriksh_referral

# Security
SECRET_KEY=your-super-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key

# Email (if using email features)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Frontend URL
FRONTEND_URL=https://yourdomain.com

# Cache
CACHE_DIR=/opt/lawvriksh-backend/cache
```

### 4. Database Setup (Run in Order)
```bash
# 1. Setup MySQL
python3 setup_mysql_db.py

# 2. Initialize database
python3 init_db.py

# 3. Add rank columns
python3 add_rank_columns.py

# 4. Create feedback table
python3 create_feedback_table.py

# 5. Create admin user
python3 create_admin.py
```

### 5. Application Deployment
```bash
# Option A: Docker Deployment (Recommended)
chmod +x setup-production.sh deploy.sh start-services.sh
./setup-production.sh
./deploy.sh

# Option B: Direct Python Deployment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.production.txt
```

### 6. Nginx Configuration
```bash
# Create nginx config
sudo nano /etc/nginx/sites-available/lawvriksh-backend
```

Nginx config content:
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/lawvriksh-backend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. SSL Certificate (Optional but Recommended)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### 8. Start Services
```bash
# Start application
./start-services.sh

# Or manually start
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 9. Verification Tests
```bash
# Test database
python3 test_db_connection.py

# Test signup
python3 test_signup_fix.py

# Test feedback
python3 test_feedback_table.py

# Full API test
python3 test_all_apis.py

# Health check
./health-check.sh
```

### 10. Process Management (Production)
```bash
# Install PM2 for process management
npm install -g pm2

# Create PM2 ecosystem file
nano ecosystem.config.js
```

PM2 config:
```javascript
module.exports = {
  apps: [{
    name: 'lawvriksh-backend',
    script: 'uvicorn',
    args: 'app.main:app --host 0.0.0.0 --port 8000 --workers 4',
    cwd: '/opt/lawvriksh-backend/BetajoiningBackend',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

```bash
# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Troubleshooting

### Common Issues:
1. **Database connection errors**: Check MySQL credentials and service status
2. **Permission errors**: Ensure proper file ownership and permissions
3. **Port conflicts**: Make sure port 8000 is available
4. **Email issues**: Verify SMTP credentials and app passwords

### Useful Commands:
```bash
# Check logs
tail -f /var/log/nginx/error.log
pm2 logs lawvriksh-backend

# Restart services
sudo systemctl restart nginx
pm2 restart lawvriksh-backend

# Check service status
sudo systemctl status mysql
sudo systemctl status nginx
pm2 status
```

## Security Considerations
- Change default passwords
- Configure firewall (ufw)
- Regular security updates
- Backup database regularly
- Use environment variables for secrets
- Enable fail2ban for SSH protection

## Backup Strategy
```bash
# Database backup
mysqldump -u lawvriksh_user -p lawvriksh_referral > backup_$(date +%Y%m%d).sql

# Application backup
tar -czf app_backup_$(date +%Y%m%d).tar.gz /opt/lawvriksh-backend
```
