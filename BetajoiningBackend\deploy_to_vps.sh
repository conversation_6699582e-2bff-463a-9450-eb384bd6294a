#!/bin/bash

# Law<PERSON>riksh Backend VPS Deployment Script
# Run this script on your VPS to deploy the application

set -e  # Exit on any error

echo "🚀 Starting LawVriksh Backend VPS Deployment"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Step 1: Validate environment
echo "📋 Step 1: Validating environment..."
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please create it first."
    exit 1
fi
print_status "Environment file found"

# Step 2: Install Python dependencies
echo "📦 Step 2: Installing Python dependencies..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    print_status "Virtual environment created"
fi

source venv/bin/activate
pip install -r requirements.txt
print_status "Dependencies installed"

# Step 3: Validate configuration
echo "🔧 Step 3: Validating configuration..."
python3 validate_config.py
if [ $? -eq 0 ]; then
    print_status "Configuration validated"
else
    print_error "Configuration validation failed"
    exit 1
fi

# Step 4: Setup database
echo "🗄️  Step 4: Setting up database..."

# Setup MySQL database and user
print_warning "Setting up MySQL database..."
python3 setup_mysql_db.py
if [ $? -eq 0 ]; then
    print_status "MySQL database setup completed"
else
    print_error "MySQL setup failed"
    exit 1
fi

# Initialize database with base schema
print_warning "Initializing database schema..."
python3 init_db.py
if [ $? -eq 0 ]; then
    print_status "Database initialized"
else
    print_error "Database initialization failed"
    exit 1
fi

# Add rank columns
print_warning "Adding rank columns..."
python3 add_rank_columns.py
if [ $? -eq 0 ]; then
    print_status "Rank columns added"
else
    print_warning "Rank columns may already exist"
fi

# Create feedback table
print_warning "Creating feedback table..."
python3 create_feedback_table.py
if [ $? -eq 0 ]; then
    print_status "Feedback table created"
else
    print_warning "Feedback table may already exist"
fi

# Step 5: Create admin user
echo "👤 Step 5: Creating admin user..."
python3 create_admin.py
if [ $? -eq 0 ]; then
    print_status "Admin user created"
else
    print_warning "Admin user may already exist"
fi

# Step 6: Run tests
echo "🧪 Step 6: Running verification tests..."

# Test database connection
python3 test_db_connection.py
if [ $? -eq 0 ]; then
    print_status "Database connection test passed"
else
    print_error "Database connection test failed"
    exit 1
fi

# Test signup functionality
python3 test_signup_fix.py
if [ $? -eq 0 ]; then
    print_status "Signup functionality test passed"
else
    print_error "Signup test failed"
    exit 1
fi

# Test feedback table
python3 test_feedback_table.py
if [ $? -eq 0 ]; then
    print_status "Feedback table test passed"
else
    print_error "Feedback table test failed"
    exit 1
fi

# Step 7: Start application
echo "🚀 Step 7: Starting application..."
print_warning "Starting backend server..."

# Check if port 8000 is available
if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null ; then
    print_warning "Port 8000 is already in use. Stopping existing process..."
    pkill -f "uvicorn.*8000" || true
    sleep 2
fi

# Start the application
nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 2 > app.log 2>&1 &
APP_PID=$!

# Wait a moment for the app to start
sleep 5

# Check if the application is running
if ps -p $APP_PID > /dev/null; then
    print_status "Application started successfully (PID: $APP_PID)"
    echo "📝 Application logs: tail -f app.log"
else
    print_error "Application failed to start"
    exit 1
fi

# Step 8: Final health check
echo "🏥 Step 8: Running health check..."
sleep 2

# Test if the API is responding
if curl -f http://localhost:8000/health >/dev/null 2>&1; then
    print_status "Health check passed"
else
    print_error "Health check failed - API not responding"
    exit 1
fi

# Step 9: Display deployment summary
echo ""
echo "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "====================================="
echo "✅ Database: Setup and initialized"
echo "✅ Tables: Users, share_events, feedback created"
echo "✅ Admin user: Created"
echo "✅ Application: Running on port 8000"
echo "✅ Health check: Passed"
echo ""
echo "📋 Next Steps:"
echo "1. Configure Nginx reverse proxy"
echo "2. Setup SSL certificate with Let's Encrypt"
echo "3. Configure firewall rules"
echo "4. Setup monitoring and backups"
echo ""
echo "🔗 API Endpoints:"
echo "   Health: http://your-domain:8000/health"
echo "   Signup: http://your-domain:8000/auth/signup"
echo "   Login:  http://your-domain:8000/auth/login"
echo ""
echo "📝 Logs: tail -f app.log"
echo "🛑 Stop: pkill -f 'uvicorn.*8000'"
echo ""
print_status "Deployment completed successfully!"
