# 🚀 VPS Deployment Checklist - <PERSON><PERSON><PERSON><PERSON> Backend

## Quick Deployment (One Command)
```bash
# Make script executable and run
chmod +x deploy_to_vps.sh
./deploy_to_vps.sh
```

## Manual Step-by-Step Deployment

### 📋 Pre-Deployment Checklist
- [ ] VPS with Ubuntu 20.04+
- [ ] Domain name configured (optional)
- [ ] SSH access to VPS
- [ ] .env file configured

### 🔧 Required Scripts (Run in Order)

#### 1. Database Setup Scripts
```bash
python3 setup_mysql_db.py          # Setup MySQL database and user
python3 init_db.py                 # Initialize base schema
python3 add_rank_columns.py        # Add ranking system columns
python3 create_feedback_table.py   # Create feedback table with email/name
```

#### 2. Configuration Scripts
```bash
python3 validate_config.py         # Validate environment configuration
python3 create_admin.py            # Create admin user
python3 update_email_password.py   # Update email credentials (optional)
```

#### 3. Testing Scripts
```bash
python3 test_db_connection.py      # Test database connectivity
python3 test_signup_fix.py         # Test user registration
python3 test_feedback_table.py     # Test feedback functionality
python3 test_all_apis.py           # Comprehensive API testing
```

#### 4. Production Scripts
```bash
chmod +x setup-production.sh       # Make production scripts executable
chmod +x start-services.sh
chmod +x deploy.sh
chmod +x health-check.sh

./setup-production.sh              # Setup production environment
./deploy.sh                        # Deploy application
./start-services.sh                # Start all services
./health-check.sh                  # Verify deployment
```

### 🗄️ Database Tables Created
- [x] `users` (with default_rank, current_rank columns)
- [x] `share_events` 
- [x] `feedback` (with email, name fields)

### 🔐 Environment Variables Required
```env
DATABASE_URL=mysql+pymysql://user:pass@localhost/db
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-key
FRONTEND_URL=https://yourdomain.com
SMTP_SERVER=smtp.gmail.com
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### 🌐 Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 🔒 SSL Setup (Optional)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### 📊 Monitoring Commands
```bash
# Check application status
ps aux | grep uvicorn

# View logs
tail -f app.log

# Check database
mysql -u lawvriksh_user -p lawvriksh_referral

# Test API health
curl http://localhost:8000/health
```

### 🛠️ Troubleshooting

#### Common Issues:
1. **Database connection failed**
   ```bash
   sudo systemctl status mysql
   python3 test_db_connection.py
   ```

2. **Port 8000 already in use**
   ```bash
   sudo lsof -i :8000
   pkill -f "uvicorn.*8000"
   ```

3. **Permission denied**
   ```bash
   sudo chown -R $USER:$USER /path/to/app
   chmod +x *.sh
   ```

4. **Email not working**
   ```bash
   python3 update_email_password.py
   python3 test_email_simple.py
   ```

### 🔄 Restart Commands
```bash
# Restart application
pkill -f "uvicorn.*8000"
uvicorn app.main:app --host 0.0.0.0 --port 8000 &

# Restart nginx
sudo systemctl restart nginx

# Restart MySQL
sudo systemctl restart mysql
```

### 📦 Production Process Management
```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start "uvicorn app.main:app --host 0.0.0.0 --port 8000" --name lawvriksh-backend

# Auto-restart on reboot
pm2 startup
pm2 save
```

### 🔍 Health Check Endpoints
- `GET /health` - Basic health check
- `GET /auth/me` - Authentication check
- `POST /auth/signup` - User registration
- `POST /feedback/submit` - Feedback submission

### 📋 Post-Deployment Verification
- [ ] API responds to health check
- [ ] User registration works
- [ ] Feedback submission works
- [ ] Admin login works
- [ ] Email notifications work (if configured)
- [ ] SSL certificate active (if configured)
- [ ] Nginx proxy working
- [ ] Database queries successful

### 🚨 Emergency Commands
```bash
# Stop everything
pkill -f uvicorn
sudo systemctl stop nginx

# Backup database
mysqldump -u lawvriksh_user -p lawvriksh_referral > emergency_backup.sql

# Restore from backup
mysql -u lawvriksh_user -p lawvriksh_referral < emergency_backup.sql
```

---

## 🎯 Quick Start Summary
1. Upload code to VPS
2. Configure `.env` file
3. Run `./deploy_to_vps.sh`
4. Configure Nginx
5. Setup SSL (optional)
6. Done! 🎉
